{"name": "langchain", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node -r ts-node/register --env-file=.env src/basicrag/ChromaDB.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@langchain/community": "^0.2.12", "cheerio": "^1.0.0-rc.12", "chromadb": "^1.8.1", "langchain": "^0.2.5", "pdf-parse": "^1.1.1"}, "devDependencies": {"@types/node": "^20.14.5", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}